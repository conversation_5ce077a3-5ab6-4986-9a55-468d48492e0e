{"// Developer Applications Management": "", "developer.applications.title": "Application Management", "developer.applications.description": "Manage your registered applications and monitor their usage", "developer.applications.refresh": "Refresh", "developer.applications.createNew": "Register Application", "// Empty State": "", "developer.applications.noAppsYet": "No Applications Yet", "developer.applications.noAppsYetDesc": "You haven't registered any applications yet. Create your first application to start integrating with GeNieGO SSO.", "developer.applications.registerFirst": "Register Your First Application", "// Application Status": "", "developer.applications.active": "Active", "developer.applications.inactive": "Inactive", "// Application Details": "", "developer.applications.clientId": "Client ID", "developer.applications.users": "Users", "developer.applications.logins": "<PERSON><PERSON>", "developer.applications.lastActivity": "Last Activity", "developer.applications.createdAt": "Created", "developer.applications.never": "Never", "developer.applications.details": "Details", "developer.applications.analytics": "Analytics", "// Actions": "", "developer.applications.actions.viewDetails": "View Details", "developer.applications.edit": "Edit Application", "developer.applications.actions.regenerateSecret": "Regenerate Secret", "developer.applications.enable": "Enable", "developer.applications.disable": "Disable", "developer.applications.delete": "Delete", "developer.applications.actions.retry": "Retry", "// Details Modal": "", "developer.applications.details.title": "Application Details", "developer.applications.details.applicationInfo": "Application Information", "developer.applications.details.description": "Description", "developer.applications.details.allowedScopes": "Allowed Scopes", "developer.applications.details.allowedRedirectUris": "Allowed Redirect URIs", "developer.applications.details.noDescription": "No description provided", "developer.applications.statistics": "Statistics", "// Error Messages": "", "developer.applications.error.loadFailed": "Failed to Load Applications", "developer.applications.error.loadFailedDesc": "Unable to load your applications. Please try again.", "developer.applications.error.connectionError": "Failed to load applications. Please check your connection and try again.", "// View Mode": "", "developer.applications.view.cards": "Card View", "developer.applications.view.table": "Table View", "// Table Headers": "", "developer.applications.table.application": "Application", "developer.applications.table.status": "Status", "developer.applications.table.users": "Users", "developer.applications.table.logins": "<PERSON><PERSON>", "developer.applications.table.created": "Created", "developer.applications.table.actions": "Actions", "// Pagination": "", "// Table View": "", "// Toast Messages": "", "developer.applications.toast.copied": "Copied to clipboard", "developer.applications.toast.copiedDesc": "Text has been copied to your clipboard.", "// Pagination Settings": "", "developer.applications.itemsPerPage": "Items per page"}