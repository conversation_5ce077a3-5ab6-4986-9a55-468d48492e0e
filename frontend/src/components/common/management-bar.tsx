"use client"

import React from 'react';
import { useIntl } from 'react-intl';
import { ChevronLeft, ChevronRight } from 'lucide-react';
import { motion } from 'motion/react';
import { SwitchToggle } from '@/components/common/switch-toggle';
import { ExpandableButton } from '@/components/common/expandable-button';
import { SlidingNumber } from '@/components/ui/animate-ui/text/sliding-number';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/shadcn/select';

interface ViewModeOption {
  value: string;
  icon: React.ReactNode;
  label: string;
}

interface ActionButton {
  icon: React.ReactNode;
  label: string;
  onClick: () => void;
  variant?: 'default' | 'primary';
  maxWidth?: string;
  ariaLabel?: string;
}

interface ManagementBarProps {
  // Pagination props
  currentPage: number;
  totalPages: number;
  onPrevPage: () => void;
  onNextPage: () => void;
  
  // Items per page props (optional)
  itemsPerPage?: number;
  onItemsPerPageChange?: (value: number) => void;
  showItemsPerPageDropdown?: boolean;
  itemsPerPageOptions?: number[];
  
  // View mode props (optional)
  viewModeOptions?: ViewModeOption[];
  currentViewMode?: string;
  onViewModeChange?: (mode: string) => void;
  showViewModeToggle?: boolean;
  
  // Action buttons props (optional)
  actionButtons?: ActionButton[];
  
  // Styling props
  className?: string;
}

/**
 * ManagementBar Component
 * 
 * A reusable management bar component that provides pagination controls,
 * optional items per page dropdown, optional view mode toggle, and configurable action buttons.
 * Addresses the padStart issue by not using it for pagination numbers.
 */
export const ManagementBar: React.FC<ManagementBarProps> = ({
  currentPage,
  totalPages,
  onPrevPage,
  onNextPage,
  itemsPerPage = 20,
  onItemsPerPageChange,
  showItemsPerPageDropdown = false,
  itemsPerPageOptions = [5, 10, 20, 50],
  viewModeOptions,
  currentViewMode,
  onViewModeChange,
  showViewModeToggle = true,
  actionButtons = [],
  className = ""
}) => {
  const intl = useIntl();

  return (
    <div className={`flex w-fit flex-wrap items-center gap-y-2 rounded-2xl border border-border bg-card p-2 ${className}`}>
      {/* Pagination Controls */}
      <div className="mx-auto flex shrink-0 items-center">
        <button
          disabled={currentPage === 1}
          className="p-1 text-muted-foreground transition-colors hover:text-foreground disabled:text-muted-foreground/30 disabled:hover:text-muted-foreground/30"
          onClick={onPrevPage}
          aria-label={intl.formatMessage({ id: 'common.pagination.previous' })}
        >
          <ChevronLeft size={20} />
        </button>
        <div className="mx-2 flex items-center space-x-1 text-sm tabular-nums">
          {/* Fixed: Removed padStart to prevent "01/1" display */}
          <SlidingNumber className="text-foreground" number={currentPage} />
          <span className="text-muted-foreground">/ {totalPages}</span>
        </div>
        <button
          disabled={currentPage === totalPages}
          className="p-1 text-muted-foreground transition-colors hover:text-foreground disabled:text-muted-foreground/30 disabled:hover:text-muted-foreground/30"
          onClick={onNextPage}
          aria-label={intl.formatMessage({ id: 'common.pagination.next' })}
        >
          <ChevronRight size={20} />
        </button>
      </div>

      {/* Separator after pagination */}
      <div className="mx-3 h-6 w-px bg-border rounded-full" />

      {/* Items per page dropdown - Only show when configured */}
      {showItemsPerPageDropdown && onItemsPerPageChange && (
        <motion.div
          initial={false}
          animate={{
            opacity: 1,
            width: 'auto',
            marginRight: '12px',
          }}
          transition={{ duration: 0.3, ease: "easeInOut" }}
          style={{ overflow: 'hidden' }}
        >
          <div className="flex items-center space-x-2">
            <span className="text-sm text-muted-foreground whitespace-nowrap">
              {intl.formatMessage({ id: 'common.itemsPerPage' })}:
            </span>
            <Select 
              value={itemsPerPage.toString()} 
              onValueChange={(value) => onItemsPerPageChange(parseInt(value))}
            >
              <SelectTrigger className="w-20 h-8">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {itemsPerPageOptions.map((option) => (
                  <SelectItem key={option} value={option.toString()}>
                    {option}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            <div className="mx-3 h-6 w-px bg-border rounded-full" />
          </div>
        </motion.div>
      )}

      {/* View Mode Toggle - Only show when configured and options provided */}
      {showViewModeToggle && viewModeOptions && currentViewMode && onViewModeChange && (
        <>
          <motion.div
            layout
            layoutRoot
            className="mx-auto flex flex-wrap sm:flex-nowrap"
          >
            <SwitchToggle
              options={viewModeOptions}
              value={currentViewMode}
              onChange={onViewModeChange}
            />
          </motion.div>
          
          {/* Separator after view mode toggle */}
          <div className="mx-3 h-6 w-px bg-border rounded-full" />
        </>
      )}

      {/* Action Buttons - Configurable from parent */}
      {actionButtons.length > 0 && (
        <motion.div
          layout
          layoutRoot
          className="mx-auto flex flex-wrap space-x-2 sm:flex-nowrap"
        >
          {actionButtons.map((button, index) => (
            <ExpandableButton
              key={index}
              icon={button.icon}
              label={button.label}
              onClick={button.onClick}
              variant={button.variant || 'default'}
              maxWidth={button.maxWidth}
              aria-label={button.ariaLabel || button.label}
            />
          ))}
        </motion.div>
      )}
    </div>
  );
};
