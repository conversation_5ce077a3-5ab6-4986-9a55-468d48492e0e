import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import { useIntl } from 'react-intl';
import {
  Plus,
  Settings,
  BarChart3,
  Eye,
  RefreshCw,
  Activity,
  Users,
  AlertCircle,
  MoreVertical,
  Edit,
  Trash2,
  Power,
  PowerOff,
  Grid3X3,
  List
} from 'lucide-react';
import { DataTableManagementBar } from '@/components/common/data-table-management-bar';
import { ApplicationCard } from '@/components/features/developer/application-card';

import { Card, CardContent } from '@/components/ui/shadcn/card';
import { Button } from '@/components/ui/shadcn/button';
import { Input } from '@/components/ui/shadcn/input';
import { Badge } from '@/components/ui/shadcn/badge';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/animate-ui/radix/dropdown-menu';
import { CopyButton } from '@/components/ui/animate-ui/button/copy';
import { Alert, AlertDescription } from '@/components/ui/shadcn/alert';
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetDescription } from '@/components/ui/animate-ui/radix/sheet';

import { motion } from 'motion/react';
import { useToast } from '@/hooks/use-toast';
import { PageLoader } from '@/components/base/page-loader';
import { useDeveloperApplications } from '@/hooks/use-developer-api';
import useAuth from '@/hooks/use-auth';




/**
 * Application Management Page
 * 
 * Allows developers to manage their registered applications including viewing,
 * editing, enabling/disabling, and monitoring usage statistics.
 */
const ApplicationManagement: React.FC = () => {
  const intl = useIntl();
  const { toast } = useToast();
  const { isAuthenticated, isLoading: authLoading } = useAuth();
  
  // Use the proper developer applications hook
  const { applications, isLoading, error, refreshData } = useDeveloperApplications(isAuthenticated && !authLoading);
  
  const [selectedApplication, setSelectedApplication] = useState<any>(null);
  const [isSheetOpen, setIsSheetOpen] = useState(false);
  const [viewMode, setViewMode] = useState<'card' | 'list'>('card');
  const [currentPage, setCurrentPage] = useState(1);
  const [tableItemsPerPage, setTableItemsPerPage] = useState(20);

  // Card view always shows 20 items, table view uses dropdown
  const itemsPerPage = viewMode === 'card' ? 20 : tableItemsPerPage;

  // Helper function to open application details in sheet
  const openApplicationDetails = (application: any) => {
    setSelectedApplication(application);
    setIsSheetOpen(true);
  };



  // Handle analytics action
  const handleAnalytics = (application: any) => {
    toast({ title: intl.formatMessage({ id: 'developer.applications.analytics' }) + " coming soon!" });
  };

  // Calculate pagination
  const totalPages = Math.ceil(applications.length / itemsPerPage);
  const startIndex = (currentPage - 1) * itemsPerPage;
  const endIndex = startIndex + itemsPerPage;
  const paginatedApplications = applications.slice(startIndex, endIndex);

  // Pagination handlers
  const handlePrevPage = () => {
    if (currentPage > 1) setCurrentPage(currentPage - 1);
  };

  const handleNextPage = () => {
    if (currentPage < totalPages) setCurrentPage(currentPage + 1);
  };

  // Render application table with horizontal scroll
  const renderApplicationTable = () => (
    <div className="bg-card border border-border rounded-lg overflow-hidden">
      <div className="overflow-x-auto max-w-full">
        <table className="w-full" style={{ minWidth: '800px' }}>
          <thead className="bg-muted/50 border-b border-border">
            <tr>
              <th className="px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider min-w-[200px]">
                {intl.formatMessage({ id: 'developer.applications.table.application' })}
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider min-w-[100px]">
                {intl.formatMessage({ id: 'developer.applications.table.status' })}
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider min-w-[80px]">
                {intl.formatMessage({ id: 'developer.applications.table.users' })}
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider min-w-[80px]">
                {intl.formatMessage({ id: 'developer.applications.table.logins' })}
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider min-w-[250px]">
                {intl.formatMessage({ id: 'developer.applications.clientId' })}
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-muted-foreground uppercase tracking-wider min-w-[120px]">
                {intl.formatMessage({ id: 'developer.applications.table.created' })}
              </th>
              <th className="px-4 py-3 text-right text-xs font-medium text-muted-foreground uppercase tracking-wider min-w-[200px]">
                {intl.formatMessage({ id: 'developer.applications.table.actions' })}
              </th>
            </tr>
          </thead>
          <tbody className="divide-y divide-border">
            {paginatedApplications.map((application) => (
              <tr key={application.id} className="hover:bg-muted/50 transition-colors">
                <td className="px-4 py-3">
                  <div className="flex items-center space-x-3">
                    <div className={`w-2 h-2 rounded-full ${application.is_active ? 'bg-green-500' : 'bg-muted-foreground/40'}`} />
                    <div>
                      <div className="font-medium text-foreground/90">{application.application_name}</div>
                    </div>
                  </div>
                </td>
                <td className="px-4 py-3">
                  <Badge variant={application.is_active ? "default" : "secondary"} className="text-xs">
                    {intl.formatMessage({ id: application.is_active ? 'developer.applications.active' : 'developer.applications.inactive' })}
                  </Badge>
                </td>
                <td className="px-4 py-3 text-sm text-foreground/90">
                  {application.stats?.total_users || 0}
                </td>
                <td className="px-4 py-3 text-sm text-foreground/90">
                  {application.stats?.total_logins || 0}
                </td>
                <td className="px-4 py-3">
                  <div className="flex items-center space-x-1">
                    <Input
                      readOnly
                      value={application.client_id}
                      className="font-mono text-xs bg-muted/30 border-muted h-7 w-40"
                    />
                    <CopyButton
                      size="sm"
                      variant="outline"
                      content={application.client_id}
                      className="h-7 w-7 p-0"
                      onCopy={() => {
                        toast({
                          title: intl.formatMessage({ id: 'developer.applications.toast.copied' }),
                          description: intl.formatMessage({ id: 'developer.applications.toast.copiedDesc' }),
                        });
                      }}
                    />
                  </div>
                </td>
                <td className="px-4 py-3 text-sm text-muted-foreground">
                  {formatDate(application.created_at)}
                </td>
                <td className="px-4 py-3 text-right">
                  <div className="flex items-center justify-end space-x-2">
                    <Button size="sm" variant="outline" onClick={() => openApplicationDetails(application)}>
                      <Eye className="h-3 w-3 mr-1" />
                      {intl.formatMessage({ id: 'developer.applications.details' })}
                    </Button>
                    <Button size="sm" variant="outline" onClick={() => toast({ title: intl.formatMessage({ id: 'developer.applications.analytics' }) + " coming soon!" })}>
                      <BarChart3 className="h-3 w-3 mr-1" />
                      {intl.formatMessage({ id: 'developer.applications.analytics' })}
                    </Button>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm" className="h-7 w-7 p-0">
                          <MoreVertical className="h-3 w-3" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => openApplicationDetails(application)}>
                          <Eye className="h-4 w-4 mr-2" />
                          {intl.formatMessage({ id: 'developer.applications.actions.viewDetails' })}
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Edit className="h-4 w-4 mr-2" />
                          {intl.formatMessage({ id: 'developer.applications.edit' })}
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <RefreshCw className="h-4 w-4 mr-2" />
                          {intl.formatMessage({ id: 'developer.applications.actions.regenerateSecret' })}
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          {application.is_active ? (
                            <>
                              <PowerOff className="h-4 w-4 mr-2" />
                              {intl.formatMessage({ id: 'developer.applications.disable' })}
                            </>
                          ) : (
                            <>
                              <Power className="h-4 w-4 mr-2" />
                              {intl.formatMessage({ id: 'developer.applications.enable' })}
                            </>
                          )}
                        </DropdownMenuItem>
                        <DropdownMenuSeparator />
                        <DropdownMenuItem className="text-red-600 dark:text-red-400">
                          <Trash2 className="h-4 w-4 mr-2" />
                          {intl.formatMessage({ id: 'developer.applications.delete' })}
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
    </div>
  );

  // Show error toast if loading fails
  useEffect(() => {
    if (error) {
      toast({
        title: intl.formatMessage({ id: 'developer.applications.error.loadFailed' }),
        description: intl.formatMessage({ id: 'developer.applications.error.loadFailedDesc' }),
        variant: "destructive",
      });
    }
  }, [error, toast]);



  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  // Show loading state
  if (authLoading || isLoading) {
    return <PageLoader />;
  }

  // Show error state
  if (error && !applications) {
    return (
      <div className="flex flex-1 flex-col gap-6 p-6 w-full">
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            {intl.formatMessage({ id: 'developer.applications.error.connectionError' })}
          </AlertDescription>
        </Alert>
        <Button onClick={refreshData} className="w-fit">
          <RefreshCw className="h-4 w-4 mr-2" />
          {intl.formatMessage({ id: 'developer.applications.actions.retry' })}
        </Button>
      </div>
    );
  }

  return (
    <div className="flex flex-1 flex-col gap-6 p-6 w-full">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-foreground/90">{intl.formatMessage({ id: 'developer.applications.title' })}</h1>
          <p className="text-muted-foreground">
            {intl.formatMessage({ id: 'developer.applications.description' })}
          </p>
        </div>
        {/* Management Bar - Using reusable ManagementBar component */}
        <DataTableManagementBar
          currentPage={currentPage}
          totalPages={totalPages}
          onPrevPage={handlePrevPage}
          onNextPage={handleNextPage}
          itemsPerPage={tableItemsPerPage}
          onItemsPerPageChange={(value) => {
            setTableItemsPerPage(value);
            setCurrentPage(1);
          }}
          showItemsPerPageDropdown={viewMode === 'list'}
          itemsPerPageOptions={[5, 10, 20, 50]}
          viewModeOptions={[
            {
              value: 'card',
              icon: <Grid3X3 size={18} />,
              label: intl.formatMessage({ id: 'developer.applications.view.cards' })
            },
            {
              value: 'list',
              icon: <List size={18} />,
              label: intl.formatMessage({ id: 'developer.applications.view.table' })
            }
          ]}
          currentViewMode={viewMode}
          onViewModeChange={(newMode) => {
            setViewMode(newMode as 'card' | 'list');
            setCurrentPage(1);
          }}
          showViewModeToggle={true}
          actionButtons={[
            {
              icon: <RefreshCw size={20} />,
              label: intl.formatMessage({ id: 'developer.applications.refresh' }),
              onClick: refreshData,
              variant: 'default',
              ariaLabel: 'Refresh'
            },
            {
              icon: <Plus size={20} />,
              label: intl.formatMessage({ id: 'developer.applications.createNew' }),
              onClick: () => window.location.href = '/developer/register',
              variant: 'primary',
              maxWidth: '180px',
              ariaLabel: 'Create New Application'
            }
          ]}
        />
      </div>

      {/* Applications Grid */}
      {!applications || applications.length === 0 ? (
        <Card>
          <CardContent className="pt-6">
            <div className="text-center py-8">
              <Settings className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">{intl.formatMessage({ id: 'developer.applications.noAppsYet' })}</h3>
              <p className="text-muted-foreground mb-4">
                {intl.formatMessage({ id: 'developer.applications.noAppsYetDesc' })}
              </p>
              <Button asChild>
                <Link to="/developer/register">
                  <Plus className="h-4 w-4 mr-2" />
                  {intl.formatMessage({ id: 'developer.applications.registerFirst' })}
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      ) : (
        <motion.div
          key={viewMode}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.3, ease: "easeInOut" }}
        >
          {viewMode === 'card' ? (
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
              {paginatedApplications.map((application) => (
                <ApplicationCard
                  key={application.id}
                  application={application}
                  onViewDetails={openApplicationDetails}
                  onAnalytics={handleAnalytics}
                  formatDate={formatDate}
                />
              ))}
            </div>
          ) : (
            renderApplicationTable()
          )}
        </motion.div>
      )}

      {/* Application Details Sheet */}
      <Sheet open={isSheetOpen} onOpenChange={setIsSheetOpen}>
        <SheetContent
          side="right"
          className="w-[600px] sm:max-w-[600px]"
          onOpenAutoFocus={(e) => e.preventDefault()}
        >
          {selectedApplication && (
            <>
              <SheetHeader className="pb-6">
                <div className="flex items-center space-x-3">
                  <div className={`w-4 h-4 rounded-full ${
                    selectedApplication.is_active ? 'bg-green-500 shadow-green-500/30 shadow-sm' : 'bg-muted-foreground/40'
                  }`} />
                  <div>
                    <SheetTitle className="text-xl font-semibold">
                      {selectedApplication.application_name}
                    </SheetTitle>
                    <SheetDescription className="text-sm text-muted-foreground mt-1">
                      {intl.formatMessage({ id: 'developer.applications.details.title' })}
                    </SheetDescription>
                  </div>
                </div>
                <div className="flex items-center gap-2 mt-4">
                  {selectedApplication.is_active ? (
                    <Badge variant="default">{intl.formatMessage({ id: 'developer.applications.active' })}</Badge>
                  ) : (
                    <Badge variant="secondary">{intl.formatMessage({ id: 'developer.applications.inactive' })}</Badge>
                  )}
                </div>
              </SheetHeader>

              <div className="space-y-8 py-4">
                {/* Application Info */}
                <div className="space-y-4">
                  <h4 className="text-lg font-semibold text-foreground border-b pb-2">
                    {intl.formatMessage({ id: 'developer.applications.details.applicationInfo' })}
                  </h4>
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="font-medium text-muted-foreground">
                        {intl.formatMessage({ id: 'developer.applications.createdAt' })}:
                      </span>
                      <p className="mt-1">{formatDate(selectedApplication.created_at)}</p>
                    </div>
                    <div>
                      <span className="font-medium text-muted-foreground">
                        {intl.formatMessage({ id: 'developer.applications.lastActivity' })}:
                      </span>
                      <p className="mt-1">{selectedApplication.stats?.last_activity || intl.formatMessage({ id: 'developer.applications.never' })}</p>
                    </div>
                  </div>
                </div>

                {/* Scopes */}
                <div className="space-y-4">
                  <h4 className="text-lg font-semibold text-foreground border-b pb-2">
                    {intl.formatMessage({ id: 'developer.applications.details.allowedScopes' })}
                  </h4>
                  <div className="flex flex-wrap gap-2">
                    {selectedApplication.allowed_scopes?.map((scope: string) => (
                      <Badge key={scope} variant="outline" className="text-xs">{scope}</Badge>
                    ))}
                  </div>
                </div>

                {/* Redirect URIs */}
                <div className="space-y-4">
                  <h4 className="text-lg font-semibold text-foreground border-b pb-2">
                    {intl.formatMessage({ id: 'developer.applications.details.allowedRedirectUris' })}
                  </h4>
                  <div className="space-y-3">
                    {selectedApplication.allowed_redirect_uris?.map((uri: string, index: number) => (
                      <div key={index} className="flex items-center space-x-2">
                        <Input readOnly value={uri} className="font-mono text-sm bg-muted/50" />
                        <CopyButton
                          size="sm"
                          variant="outline"
                          content={uri}
                          onCopy={() => {
                            toast({
                              title: intl.formatMessage({ id: 'developer.applications.toast.copied' }),
                              description: intl.formatMessage({ id: 'developer.applications.toast.copiedDesc' }),
                            });
                          }}
                        />
                      </div>
                    ))}
                  </div>
                </div>

                {/* Description */}
                <div className="space-y-4">
                  <h4 className="text-lg font-semibold text-foreground border-b pb-2">
                    {intl.formatMessage({ id: 'developer.applications.details.description' })}
                  </h4>
                  <p className="text-sm text-muted-foreground leading-relaxed">
                    {selectedApplication.description || intl.formatMessage({ id: 'developer.applications.details.noDescription' })}
                  </p>
                </div>

                {/* Statistics */}
                <div className="space-y-4">
                  <h4 className="text-lg font-semibold text-foreground border-b pb-2">
                    {intl.formatMessage({ id: 'developer.applications.statistics' })}
                  </h4>
                  <div className="grid grid-cols-2 gap-4">
                    <div className="p-4 bg-muted/30 rounded-lg text-center">
                      <Users className="h-6 w-6 mx-auto mb-2 text-blue-600 dark:text-blue-400" />
                      <p className="text-2xl font-bold text-foreground">{selectedApplication.stats?.total_users?.toLocaleString() || 0}</p>
                      <p className="text-sm text-muted-foreground font-medium">{intl.formatMessage({ id: 'developer.applications.users' })}</p>
                    </div>
                    <div className="p-4 bg-muted/30 rounded-lg text-center">
                      <Activity className="h-6 w-6 mx-auto mb-2 text-green-600 dark:text-green-400" />
                      <p className="text-2xl font-bold text-foreground">{selectedApplication.stats?.total_logins?.toLocaleString() || 0}</p>
                      <p className="text-sm text-muted-foreground font-medium">{intl.formatMessage({ id: 'developer.applications.logins' })}</p>
                    </div>
                  </div>
                </div>
              </div>
            </>
          )}
        </SheetContent>
      </Sheet>
    </div>
  );
};

export default ApplicationManagement;
